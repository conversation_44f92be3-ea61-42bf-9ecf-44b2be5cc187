/**
 * 创建测试用户和项目脚本
 * 用于创建测试数据以便诊断项目卡片点击问题
 */

const fs = require('fs');

// 颜色输出函数
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const color = {
    'error': colors.red,
    'success': colors.green,
    'warning': colors.yellow,
    'info': colors.blue
  }[type] || colors.blue;
  
  console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

function logError(message) { log(message, 'error'); }
function logSuccess(message) { log(message, 'success'); }
function logWarning(message) { log(message, 'warning'); }
function logInfo(message) { log(message, 'info'); }

// 测试配置
const TEST_CONFIG = {
  API_BASE_URL: 'http://localhost:3000',
  // 使用现有的用户和项目进行测试
  EXISTING_USER: {
    username: 'zgledu',
    password: 'admin123' // 尝试常见密码
  },
  EXISTING_PROJECT: {
    id: 'e2394dc0-833f-4973-9ac9-7522529e1497',
    name: '我的项目2'
  },
  EXISTING_SCENE: {
    id: '7dc0fa11-9a71-11f0-9fcc-9270c722221e',
    name: '默认场景'
  },
  TEST_USER: {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'testuser123',
    displayName: '测试用户'
  },
  TEST_PROJECT: {
    name: '测试项目',
    description: '用于测试项目卡片点击功能的项目',
    type: 'scene'
  },
  TEST_SCENE: {
    name: '测试场景',
    description: '用于测试的默认场景'
  }
};

/**
 * 注册测试用户
 */
async function registerTestUser() {
  logInfo('👤 注册测试用户...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_CONFIG.TEST_USER)
    });
    
    if (response.ok) {
      const data = await response.json();
      logSuccess('✅ 测试用户注册成功');
      return data.data;
    } else {
      const errorData = await response.json();
      if (response.status === 409) {
        logWarning('⚠️ 测试用户已存在，尝试登录...');
        return await loginTestUser();
      } else {
        logError(`❌ 用户注册失败: ${errorData.message || response.statusText}`);
        return null;
      }
    }
  } catch (error) {
    logError(`❌ 注册请求失败: ${error.message}`);
    return null;
  }
}

/**
 * 登录测试用户
 */
async function loginTestUser() {
  logInfo('🔐 登录测试用户...');

  try {
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        usernameOrEmail: TEST_CONFIG.TEST_USER.username,
        password: TEST_CONFIG.TEST_USER.password
      })
    });

    if (response.ok) {
      const data = await response.json();
      logSuccess('✅ 测试用户登录成功');
      return data.data;
    } else {
      const errorData = await response.json();
      logError(`❌ 用户登录失败: ${errorData.message || response.statusText}`);
      return null;
    }
  } catch (error) {
    logError(`❌ 登录请求失败: ${error.message}`);
    return null;
  }
}

/**
 * 登录现有用户
 */
async function loginExistingUser() {
  logInfo('🔐 登录现有用户 zgledu...');

  // 尝试多个可能的密码
  const possiblePasswords = ['admin123', 'zgledu123', '123456', 'password', 'zgledu'];

  for (const password of possiblePasswords) {
    try {
      logInfo(`尝试密码: ${password}`);
      const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usernameOrEmail: TEST_CONFIG.EXISTING_USER.username,
          password: password
        })
      });

      if (response.ok) {
        const data = await response.json();
        logSuccess(`✅ 现有用户登录成功，密码: ${password}`);
        return data.data;
      } else {
        const errorData = await response.json();
        logWarning(`❌ 密码 ${password} 失败: ${errorData.message || response.statusText}`);
      }
    } catch (error) {
      logError(`❌ 登录请求失败: ${error.message}`);
    }
  }

  logError('❌ 所有密码尝试都失败了');
  return null;
}

/**
 * 创建测试项目
 */
async function createTestProject(token) {
  logInfo('📁 创建测试项目...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/projects`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_CONFIG.TEST_PROJECT)
    });
    
    if (response.ok) {
      const data = await response.json();
      logSuccess(`✅ 测试项目创建成功: ${data.data.name} (${data.data.id})`);
      return data.data;
    } else {
      const errorData = await response.json();
      logError(`❌ 项目创建失败: ${errorData.message || response.statusText}`);
      return null;
    }
  } catch (error) {
    logError(`❌ 项目创建请求失败: ${error.message}`);
    return null;
  }
}

/**
 * 创建测试场景
 */
async function createTestScene(token, projectId) {
  logInfo('🎬 创建测试场景...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/projects/${projectId}/scenes`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_CONFIG.TEST_SCENE)
    });
    
    if (response.ok) {
      const data = await response.json();
      logSuccess(`✅ 测试场景创建成功: ${data.data.name} (${data.data.id})`);
      return data.data;
    } else {
      const errorData = await response.json();
      logError(`❌ 场景创建失败: ${errorData.message || response.statusText}`);
      return null;
    }
  } catch (error) {
    logError(`❌ 场景创建请求失败: ${error.message}`);
    return null;
  }
}

/**
 * 测试场景数据访问
 */
async function testSceneDataAccess(token, projectId, sceneId) {
  logInfo('🎯 测试场景数据访问...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/projects/${projectId}/scenes/${sceneId}/data`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      logSuccess('✅ 场景数据访问成功');
      logInfo(`场景数据: ${JSON.stringify(data.data, null, 2)}`);
      return true;
    } else {
      logError(`❌ 场景数据访问失败: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      logError(`错误详情: ${errorText}`);
      return false;
    }
  } catch (error) {
    logError(`❌ 场景数据访问请求失败: ${error.message}`);
    return false;
  }
}

/**
 * 生成前端测试页面
 */
function generateTestPage(projectId, sceneId, token) {
  logInfo('🌐 生成前端测试页面...');
  
  const testPageContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目卡片点击功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .log-area {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .status.error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .status.warning { background: #fffbe6; border: 1px solid #ffe58f; color: #faad14; }
    </style>
</head>
<body>
    <h1>项目卡片点击功能测试</h1>
    
    <div class="test-card" onclick="testProjectCardClick()">
        <h3>🎯 测试项目</h3>
        <p>点击此卡片模拟项目卡片点击功能</p>
        <p><strong>项目ID:</strong> ${projectId}</p>
        <p><strong>场景ID:</strong> ${sceneId}</p>
    </div>
    
    <div>
        <button class="test-button" onclick="testApiAccess()">测试API访问</button>
        <button class="test-button" onclick="testSceneData()">测试场景数据</button>
        <button class="test-button" onclick="simulateEditorNavigation()">模拟编辑器导航</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="status" class="status"></div>
    <div id="log" class="log-area"></div>
    
    <script>
        const PROJECT_ID = '${projectId}';
        const SCENE_ID = '${sceneId}';
        const TOKEN = '${token}';
        const API_BASE_URL = 'http://localhost:3000';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logElement = document.getElementById('log');
            const color = {
                'error': '#ff4d4f',
                'success': '#52c41a',
                'warning': '#faad14',
                'info': '#1890ff'
            }[type] || '#00ff00';
            
            logElement.innerHTML += \`<div style="color: \${color}">[${timestamp}] \${message}</div>\`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.className = \`status \${type}\`;
            statusElement.textContent = message;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('status').textContent = '';
        }
        
        async function testApiAccess() {
            log('开始测试API访问...', 'info');
            
            try {
                const response = await fetch(\`\${API_BASE_URL}/api/projects\`, {
                    headers: {
                        'Authorization': \`Bearer \${TOKEN}\`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(\`✅ API访问成功，获取到 \${data.data?.length || 0} 个项目\`, 'success');
                    setStatus('API访问正常', 'success');
                } else {
                    log(\`❌ API访问失败: \${response.status} \${response.statusText}\`, 'error');
                    setStatus('API访问失败', 'error');
                }
            } catch (error) {
                log(\`❌ API请求异常: \${error.message}\`, 'error');
                setStatus('API请求异常', 'error');
            }
        }
        
        async function testSceneData() {
            log('开始测试场景数据获取...', 'info');
            
            try {
                const response = await fetch(\`\${API_BASE_URL}/api/projects/\${PROJECT_ID}/scenes/\${SCENE_ID}/data\`, {
                    headers: {
                        'Authorization': \`Bearer \${TOKEN}\`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ 场景数据获取成功', 'success');
                    log(\`场景数据结构: \${JSON.stringify(Object.keys(data.data || {}), null, 2)}\`, 'info');
                    setStatus('场景数据访问正常', 'success');
                } else {
                    log(\`❌ 场景数据获取失败: \${response.status} \${response.statusText}\`, 'error');
                    setStatus('场景数据访问失败', 'error');
                }
            } catch (error) {
                log(\`❌ 场景数据请求异常: \${error.message}\`, 'error');
                setStatus('场景数据请求异常', 'error');
            }
        }
        
        async function testProjectCardClick() {
            log('🎯 模拟项目卡片点击...', 'info');
            
            try {
                // 1. 获取项目场景列表
                log('1. 获取项目场景列表...', 'info');
                const scenesResponse = await fetch(\`\${API_BASE_URL}/api/projects/\${PROJECT_ID}/scenes\`, {
                    headers: {
                        'Authorization': \`Bearer \${TOKEN}\`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!scenesResponse.ok) {
                    throw new Error(\`场景列表获取失败: \${scenesResponse.status}\`);
                }
                
                const scenesData = await scenesResponse.json();
                log(\`✅ 场景列表获取成功，共 \${scenesData.data?.length || 0} 个场景\`, 'success');
                
                if (!scenesData.data || scenesData.data.length === 0) {
                    throw new Error('项目中没有场景');
                }
                
                // 2. 获取场景数据
                log('2. 获取场景数据...', 'info');
                const sceneDataResponse = await fetch(\`\${API_BASE_URL}/api/projects/\${PROJECT_ID}/scenes/\${SCENE_ID}/data\`, {
                    headers: {
                        'Authorization': \`Bearer \${TOKEN}\`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!sceneDataResponse.ok) {
                    throw new Error(\`场景数据获取失败: \${sceneDataResponse.status}\`);
                }
                
                const sceneData = await sceneDataResponse.json();
                log('✅ 场景数据获取成功', 'success');
                
                // 3. 模拟路由跳转
                log('3. 模拟路由跳转到编辑器...', 'info');
                const editorUrl = \`/editor/\${PROJECT_ID}/\${SCENE_ID}\`;
                log(\`编辑器URL: \${editorUrl}\`, 'info');
                
                // 4. 测试完成
                log('✅ 项目卡片点击功能测试通过！', 'success');
                setStatus('项目卡片点击功能正常', 'success');
                
            } catch (error) {
                log(\`❌ 测试失败: \${error.message}\`, 'error');
                setStatus('项目卡片点击功能异常', 'error');
            }
        }
        
        function simulateEditorNavigation() {
            log('🚀 模拟编辑器页面导航...', 'info');
            const editorUrl = \`http://localhost/editor/\${PROJECT_ID}/\${SCENE_ID}\`;
            log(\`尝试打开编辑器: \${editorUrl}\`, 'info');
            window.open(editorUrl, '_blank');
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            log('页面加载完成，开始自动测试...', 'info');
            setTimeout(testApiAccess, 1000);
        };
    </script>
</body>
</html>
  `;
  
  fs.writeFileSync('test-project-card-click.html', testPageContent);
  logSuccess('✅ 前端测试页面已生成: test-project-card-click.html');
}

/**
 * 测试现有项目和场景
 */
async function testExistingProjectAndScene() {
  logInfo('🎯 测试现有项目和场景...');

  try {
    // 1. 尝试登录现有用户
    const userData = await loginExistingUser();
    if (!userData) {
      logError('❌ 无法登录现有用户，尝试创建新的测试数据');
      return await createNewTestData();
    }

    const token = userData.token;
    logInfo(`使用令牌: ${token.substring(0, 20)}...`);

    // 2. 测试现有项目的场景数据访问
    const sceneDataAccessible = await testSceneDataAccess(
      token,
      TEST_CONFIG.EXISTING_PROJECT.id,
      TEST_CONFIG.EXISTING_SCENE.id
    );

    // 3. 生成测试页面
    generateTestPage(
      TEST_CONFIG.EXISTING_PROJECT.id,
      TEST_CONFIG.EXISTING_SCENE.id,
      token
    );

    // 4. 输出总结
    logSuccess('🎉 现有项目测试完成！');
    logInfo(`测试用户: ${TEST_CONFIG.EXISTING_USER.username}`);
    logInfo(`测试项目: ${TEST_CONFIG.EXISTING_PROJECT.name} (${TEST_CONFIG.EXISTING_PROJECT.id})`);
    logInfo(`测试场景: ${TEST_CONFIG.EXISTING_SCENE.name} (${TEST_CONFIG.EXISTING_SCENE.id})`);
    logInfo(`场景数据访问: ${sceneDataAccessible ? '✅ 正常' : '❌ 异常'}`);
    logInfo('');
    logInfo('📋 下一步操作:');
    logInfo('1. 打开浏览器访问 http://localhost/');
    logInfo(`2. 使用用户名 ${TEST_CONFIG.EXISTING_USER.username} 登录`);
    logInfo('3. 点击"我的项目2"卡片，观察是否能正常进入编辑器');
    logInfo('4. 或者打开 test-project-card-click.html 进行详细测试');

    return true;

  } catch (error) {
    logError(`❌ 测试现有项目失败: ${error.message}`);
    console.error(error);
    return false;
  }
}

/**
 * 创建新的测试数据
 */
async function createNewTestData() {
  logInfo('🚀 开始创建新的测试数据...');

  try {
    // 1. 注册或登录测试用户
    const userData = await registerTestUser();
    if (!userData) {
      logError('❌ 无法获取测试用户，退出');
      return false;
    }

    const token = userData.token;
    logInfo(`使用令牌: ${token.substring(0, 20)}...`);

    // 2. 创建测试项目
    const project = await createTestProject(token);
    if (!project) {
      logError('❌ 无法创建测试项目，退出');
      return false;
    }

    // 3. 创建测试场景
    const scene = await createTestScene(token, project.id);
    if (!scene) {
      logError('❌ 无法创建测试场景，退出');
      return false;
    }

    // 4. 测试场景数据访问
    const sceneDataAccessible = await testSceneDataAccess(token, project.id, scene.id);

    // 5. 生成测试页面
    generateTestPage(project.id, scene.id, token);

    // 6. 输出总结
    logSuccess('🎉 新测试数据创建完成！');
    logInfo(`测试用户: ${TEST_CONFIG.TEST_USER.username}`);
    logInfo(`测试项目: ${project.name} (${project.id})`);
    logInfo(`测试场景: ${scene.name} (${scene.id})`);
    logInfo(`场景数据访问: ${sceneDataAccessible ? '✅ 正常' : '❌ 异常'}`);
    logInfo('');
    logInfo('📋 下一步操作:');
    logInfo('1. 打开浏览器访问 http://localhost/');
    logInfo(`2. 使用用户名 ${TEST_CONFIG.TEST_USER.username} 和密码 ${TEST_CONFIG.TEST_USER.password} 登录`);
    logInfo('3. 点击测试项目卡片，观察是否能正常进入编辑器');
    logInfo('4. 或者打开 test-project-card-click.html 进行详细测试');

    return true;

  } catch (error) {
    logError(`❌ 创建测试数据失败: ${error.message}`);
    console.error(error);
    return false;
  }
}

/**
 * 主流程
 */
async function main() {
  logInfo('🚀 开始项目卡片点击功能测试...');

  // 首先尝试测试现有的项目和场景
  const existingTestSuccess = await testExistingProjectAndScene();

  // 如果现有测试失败，创建新的测试数据
  if (!existingTestSuccess) {
    logWarning('⚠️ 现有项目测试失败，创建新的测试数据...');
    await createNewTestData();
  }
}

// 运行主流程
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  main,
  registerTestUser,
  loginTestUser,
  createTestProject,
  createTestScene
};
