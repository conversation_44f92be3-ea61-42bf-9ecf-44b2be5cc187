FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制协作服务代码
COPY collaboration-service/package*.json ./

# 安装依赖
RUN npm install

# 复制协作服务源代码
COPY collaboration-service/src ./src
COPY collaboration-service/tsconfig.json ./
COPY collaboration-service/nest-cli.json ./

# 构建应用
RUN npm run build

# 生产环境
FROM node:22-alpine

# 配置Alpine镜像源并安装wget用于健康检查
RUN echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/main" > /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/community" >> /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache wget ca-certificates

WORKDIR /app

# 复制package.json和package-lock.json
COPY --from=builder /app/package*.json ./

# 仅安装生产依赖
RUN npm install --only=production

# 复制构建产物
COPY --from=builder /app/dist ./dist

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3005

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD wget -qO- http://localhost:$PORT/health || exit 1

# 暴露端口
EXPOSE $PORT

# 启动应用
CMD ["node", "dist/main.js"]
