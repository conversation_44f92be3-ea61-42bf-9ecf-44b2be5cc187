/**
 * 项目卡片点击功能诊断脚本
 * 用于诊断前端登录后点击项目卡片无法进入编辑器的问题
 */

const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const color = {
    'error': colors.red,
    'success': colors.green,
    'warning': colors.yellow,
    'info': colors.blue
  }[type] || colors.white;
  
  console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

function logError(message) { log(message, 'error'); }
function logSuccess(message) { log(message, 'success'); }
function logWarning(message) { log(message, 'warning'); }
function logInfo(message) { log(message, 'info'); }

// 测试配置
const TEST_CONFIG = {
  API_BASE_URL: 'http://localhost:3000',
  FRONTEND_URL: 'http://localhost:80',
  TEST_USER: {
    username: 'testuser',
    password: 'testuser123'
  }
};

/**
 * 检查服务状态
 */
async function checkServices() {
  logInfo('🔍 检查服务状态...');
  
  const services = [
    { name: 'API网关', url: `${TEST_CONFIG.API_BASE_URL}/api/health` },
    { name: '前端编辑器', url: `${TEST_CONFIG.FRONTEND_URL}` },
    { name: '项目服务', url: `${TEST_CONFIG.API_BASE_URL}/api/projects` },
  ];
  
  for (const service of services) {
    try {
      const response = await fetch(service.url);
      if (response.ok) {
        logSuccess(`✅ ${service.name} 运行正常`);
      } else {
        logError(`❌ ${service.name} 返回错误状态: ${response.status}`);
      }
    } catch (error) {
      logError(`❌ ${service.name} 连接失败: ${error.message}`);
    }
  }
}

/**
 * 测试用户认证
 */
async function testAuthentication() {
  logInfo('🔐 测试用户认证...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        usernameOrEmail: TEST_CONFIG.TEST_USER.username,
        password: TEST_CONFIG.TEST_USER.password
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      logSuccess('✅ 用户认证成功');
      return data.data.token;
    } else {
      const errorData = await response.json();
      logError(`❌ 用户认证失败: ${errorData.message || response.statusText}`);
      return null;
    }
  } catch (error) {
    logError(`❌ 认证请求失败: ${error.message}`);
    return null;
  }
}

/**
 * 测试项目列表获取
 */
async function testProjectsList(token) {
  logInfo('📋 测试项目列表获取...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/projects`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      logSuccess(`✅ 项目列表获取成功，共 ${data.data?.length || 0} 个项目`);
      return data.data || [];
    } else {
      logError(`❌ 项目列表获取失败: ${response.status} ${response.statusText}`);
      return [];
    }
  } catch (error) {
    logError(`❌ 项目列表请求失败: ${error.message}`);
    return [];
  }
}

/**
 * 测试场景列表获取
 */
async function testScenesList(token, projectId) {
  logInfo(`🎬 测试项目 ${projectId} 的场景列表获取...`);
  
  try {
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/projects/${projectId}/scenes`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      logSuccess(`✅ 场景列表获取成功，共 ${data.data?.length || 0} 个场景`);
      return data.data || [];
    } else {
      logError(`❌ 场景列表获取失败: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      logError(`错误详情: ${errorText}`);
      return [];
    }
  } catch (error) {
    logError(`❌ 场景列表请求失败: ${error.message}`);
    return [];
  }
}

/**
 * 测试场景数据获取
 */
async function testSceneData(token, projectId, sceneId) {
  logInfo(`🎯 测试场景数据获取 (项目: ${projectId}, 场景: ${sceneId})...`);
  
  try {
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/api/projects/${projectId}/scenes/${sceneId}/data`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      logSuccess('✅ 场景数据获取成功');
      logInfo(`场景数据结构: ${JSON.stringify(Object.keys(data.data || {}), null, 2)}`);
      return data.data;
    } else {
      logError(`❌ 场景数据获取失败: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      logError(`错误详情: ${errorText}`);
      return null;
    }
  } catch (error) {
    logError(`❌ 场景数据请求失败: ${error.message}`);
    return null;
  }
}

/**
 * 检查前端配置
 */
async function checkFrontendConfig() {
  logInfo('⚙️ 检查前端配置...');
  
  try {
    // 检查前端是否可访问
    const response = await fetch(`${TEST_CONFIG.FRONTEND_URL}`);
    if (response.ok) {
      logSuccess('✅ 前端页面可访问');
      
      // 检查前端的API配置
      const html = await response.text();
      if (html.includes('__ENV__')) {
        logInfo('✅ 前端包含环境变量配置');
      } else {
        logWarning('⚠️ 前端可能缺少环境变量配置');
      }
    } else {
      logError(`❌ 前端页面访问失败: ${response.status}`);
    }
  } catch (error) {
    logError(`❌ 前端配置检查失败: ${error.message}`);
  }
}

/**
 * 生成诊断报告
 */
function generateDiagnosisReport(results) {
  logInfo('📊 生成诊断报告...');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      servicesHealthy: results.servicesHealthy || false,
      authenticationWorking: results.authenticationWorking || false,
      projectsAccessible: results.projectsAccessible || false,
      scenesAccessible: results.scenesAccessible || false,
      sceneDataAccessible: results.sceneDataAccessible || false
    },
    details: results.details || [],
    recommendations: []
  };
  
  // 生成建议
  if (!report.summary.servicesHealthy) {
    report.recommendations.push('检查Docker容器状态，确保所有服务正常运行');
  }
  
  if (!report.summary.authenticationWorking) {
    report.recommendations.push('检查用户服务和认证配置');
  }
  
  if (!report.summary.projectsAccessible) {
    report.recommendations.push('检查项目服务和API网关路由配置');
  }
  
  if (!report.summary.scenesAccessible) {
    report.recommendations.push('检查场景服务路由和权限配置');
  }
  
  if (!report.summary.sceneDataAccessible) {
    report.recommendations.push('检查场景数据API路由实现');
  }
  
  // 保存报告
  fs.writeFileSync('diagnosis-report.json', JSON.stringify(report, null, 2));
  logSuccess('✅ 诊断报告已保存到 diagnosis-report.json');
  
  return report;
}

/**
 * 主诊断流程
 */
async function runDiagnosis() {
  logInfo('🚀 开始项目卡片点击功能诊断...');
  
  const results = {
    details: []
  };
  
  try {
    // 1. 检查服务状态
    await checkServices();
    results.servicesHealthy = true;
    
    // 2. 检查前端配置
    await checkFrontendConfig();
    
    // 3. 测试认证
    const token = await testAuthentication();
    results.authenticationWorking = !!token;
    
    if (!token) {
      logError('❌ 认证失败，无法继续测试');
      return generateDiagnosisReport(results);
    }
    
    // 4. 测试项目列表
    const projects = await testProjectsList(token);
    results.projectsAccessible = projects.length > 0;
    
    if (projects.length === 0) {
      logWarning('⚠️ 没有找到项目，创建测试项目...');
      // 这里可以添加创建测试项目的逻辑
    }
    
    // 5. 测试场景列表
    if (projects.length > 0) {
      const project = projects[0];
      logInfo(`使用项目进行测试: ${project.name} (${project.id})`);
      
      const scenes = await testScenesList(token, project.id);
      results.scenesAccessible = scenes.length >= 0; // 允许空场景列表
      
      // 6. 测试场景数据
      if (scenes.length > 0) {
        const scene = scenes[0];
        logInfo(`使用场景进行测试: ${scene.name} (${scene.id})`);
        
        const sceneData = await testSceneData(token, project.id, scene.id);
        results.sceneDataAccessible = !!sceneData;
      } else {
        logWarning('⚠️ 项目中没有场景，这可能是问题的原因');
        results.details.push('项目中没有场景，用户点击项目卡片时可能无法跳转到编辑器');
      }
    }
    
    // 生成报告
    const report = generateDiagnosisReport(results);
    
    // 输出总结
    logInfo('📋 诊断总结:');
    Object.entries(report.summary).forEach(([key, value]) => {
      const status = value ? '✅' : '❌';
      logInfo(`${status} ${key}: ${value}`);
    });
    
    if (report.recommendations.length > 0) {
      logInfo('💡 建议:');
      report.recommendations.forEach((rec, index) => {
        logInfo(`${index + 1}. ${rec}`);
      });
    }
    
  } catch (error) {
    logError(`❌ 诊断过程中发生错误: ${error.message}`);
    console.error(error);
  }
}

// 运行诊断
if (require.main === module) {
  runDiagnosis().catch(console.error);
}

module.exports = {
  runDiagnosis,
  checkServices,
  testAuthentication,
  testProjectsList,
  testScenesList,
  testSceneData
};
