FROM node:22-alpine AS builder

WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./
RUN npm install

# 复制源代码
COPY . ./

RUN npm run build

FROM node:22-alpine

WORKDIR /app

# 配置Alpine镜像源并安装curl用于健康检查
RUN echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/main" > /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/community" >> /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache curl ca-certificates

COPY --from=builder /app/package*.json ./
COPY --from=builder /app/dist ./dist

RUN npm install --only=production

# 创建上传目录
RUN mkdir -p /app/uploads/images /app/uploads/models /app/uploads/audio /app/uploads/other

EXPOSE 3000

CMD ["node", "dist/main.js"]
