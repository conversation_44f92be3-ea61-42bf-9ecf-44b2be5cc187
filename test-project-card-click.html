
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目卡片点击功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .test-button.success {
            background: #52c41a;
        }
        .test-button.error {
            background: #ff4d4f;
        }
        .log-area {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .status.error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .status.warning { background: #fffbe6; border: 1px solid #ffe58f; color: #faad14; }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 项目卡片点击功能测试</h1>
        <p>此页面用于测试前端项目卡片点击功能是否正常工作</p>
    </div>
    
    <div class="info-grid">
        <div class="info-card">
            <h3>📋 测试信息</h3>
            <p><strong>用户名:</strong> testuser1759142685016</p>
            <p><strong>项目ID:</strong> 727517ef-bcc9-4826-8f20-fde01b807699</p>
            <p><strong>场景ID:</strong> f18bf301-a340-468c-8fd1-b1884006bad3</p>
        </div>
        <div class="info-card">
            <h3>🔗 相关链接</h3>
            <p><a href="http://localhost/" target="_blank">前端编辑器</a></p>
            <p><a href="http://localhost/editor/727517ef-bcc9-4826-8f20-fde01b807699/f18bf301-a340-468c-8fd1-b1884006bad3" target="_blank">直接访问编辑器</a></p>
        </div>
    </div>
    
    <div class="test-card" onclick="testProjectCardClick()">
        <h3>🎯 模拟项目卡片</h3>
        <p>点击此卡片模拟项目卡片点击功能</p>
        <p><strong>项目名称:</strong> 项目卡片测试项目</p>
    </div>
    
    <div>
        <button class="test-button" onclick="testApiAccess()">测试API访问</button>
        <button class="test-button" onclick="testSceneData()">测试场景数据</button>
        <button class="test-button" onclick="testProjectsList()">测试项目列表</button>
        <button class="test-button" onclick="testScenesList()">测试场景列表</button>
        <button class="test-button" onclick="simulateEditorNavigation()">打开编辑器</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="status" class="status"></div>
    <div id="log" class="log-area"></div>
    
    <script>
        const PROJECT_ID = '727517ef-bcc9-4826-8f20-fde01b807699';
        const SCENE_ID = 'f18bf301-a340-468c-8fd1-b1884006bad3';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTkxNDI2ODUsImV4cCI6MTc1OTIyOTA4NX0.U9Oc36hfKYMzk9HBQf_K1cM2lDbFFihaWagi2e3qTyo';
        const API_BASE_URL = 'http://localhost:3000';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logElement = document.getElementById('log');
            const color = {
                'error': '#ff4d4f',
                'success': '#52c41a',
                'warning': '#faad14',
                'info': '#1890ff'
            }[type] || '#00ff00';
            
            logElement.innerHTML += `<div style="color: ${color}">[1759142685016] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.className = `status ${type}`;
            statusElement.textContent = message;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('status').textContent = '';
        }
        
        async function testApiAccess() {
            log('开始测试API访问...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ API网关访问成功', 'success');
                    setStatus('API网关正常', 'success');
                } else {
                    log(`❌ API网关访问失败: ${response.status} ${response.statusText}`, 'error');
                    setStatus('API网关异常', 'error');
                }
            } catch (error) {
                log(`❌ API请求异常: ${error.message}`, 'error');
                setStatus('API请求异常', 'error');
            }
        }
        
        async function testProjectsList() {
            log('开始测试项目列表获取...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/projects`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 项目列表获取成功，共 ${data.data?.length || 0} 个项目`, 'success');
                    setStatus('项目列表访问正常', 'success');
                } else {
                    log(`❌ 项目列表获取失败: ${response.status} ${response.statusText}`, 'error');
                    setStatus('项目列表访问失败', 'error');
                }
            } catch (error) {
                log(`❌ 项目列表请求异常: ${error.message}`, 'error');
                setStatus('项目列表请求异常', 'error');
            }
        }
        
        async function testScenesList() {
            log('开始测试场景列表获取...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/projects/${PROJECT_ID}/scenes`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 场景列表获取成功，共 ${data.data?.length || 0} 个场景`, 'success');
                    setStatus('场景列表访问正常', 'success');
                } else {
                    log(`❌ 场景列表获取失败: ${response.status} ${response.statusText}`, 'error');
                    setStatus('场景列表访问失败', 'error');
                }
            } catch (error) {
                log(`❌ 场景列表请求异常: ${error.message}`, 'error');
                setStatus('场景列表请求异常', 'error');
            }
        }
        
        async function testSceneData() {
            log('开始测试场景数据获取...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/projects/${PROJECT_ID}/scenes/${SCENE_ID}/data`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ 场景数据获取成功', 'success');
                    log(`场景数据结构: ${JSON.stringify(Object.keys(data.data || {}), null, 2)}`, 'info');
                    setStatus('场景数据访问正常', 'success');
                } else {
                    log(`❌ 场景数据获取失败: ${response.status} ${response.statusText}`, 'error');
                    setStatus('场景数据访问失败', 'error');
                }
            } catch (error) {
                log(`❌ 场景数据请求异常: ${error.message}`, 'error');
                setStatus('场景数据请求异常', 'error');
            }
        }
        
        async function testProjectCardClick() {
            log('🎯 模拟项目卡片点击流程...', 'info');
            
            try {
                // 1. 获取项目场景列表
                log('1. 获取项目场景列表...', 'info');
                const scenesResponse = await fetch(`${API_BASE_URL}/api/projects/${PROJECT_ID}/scenes`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!scenesResponse.ok) {
                    throw new Error(`场景列表获取失败: ${scenesResponse.status}`);
                }
                
                const scenesData = await scenesResponse.json();
                log(`✅ 场景列表获取成功，共 ${scenesData.data?.length || 0} 个场景`, 'success');
                
                if (!scenesData.data || scenesData.data.length === 0) {
                    throw new Error('项目中没有场景');
                }
                
                // 2. 获取场景数据
                log('2. 获取场景数据...', 'info');
                const sceneDataResponse = await fetch(`${API_BASE_URL}/api/projects/${PROJECT_ID}/scenes/${SCENE_ID}/data`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!sceneDataResponse.ok) {
                    throw new Error(`场景数据获取失败: ${sceneDataResponse.status}`);
                }
                
                const sceneData = await sceneDataResponse.json();
                log('✅ 场景数据获取成功', 'success');
                
                // 3. 模拟路由跳转
                log('3. 模拟路由跳转到编辑器...', 'info');
                const editorUrl = `/editor/${PROJECT_ID}/${SCENE_ID}`;
                log(`编辑器URL: ${editorUrl}`, 'info');
                
                // 4. 测试完成
                log('✅ 项目卡片点击功能测试通过！', 'success');
                setStatus('项目卡片点击功能正常', 'success');
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
                setStatus('项目卡片点击功能异常', 'error');
            }
        }
        
        function simulateEditorNavigation() {
            log('🚀 打开编辑器页面...', 'info');
            const editorUrl = `http://localhost/editor/${PROJECT_ID}/${SCENE_ID}`;
            log(`编辑器URL: ${editorUrl}`, 'info');
            window.open(editorUrl, '_blank');
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            log('页面加载完成，开始自动测试...', 'info');
            setTimeout(testApiAccess, 1000);
            setTimeout(testProjectsList, 2000);
            setTimeout(testScenesList, 3000);
            setTimeout(testSceneData, 4000);
        };
    </script>
</body>
</html>
  