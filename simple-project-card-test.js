#!/usr/bin/env node

/**
 * 简化的项目卡片点击功能测试脚本
 * 使用Node.js内置模块测试API连接性
 */

const http = require('http');
const https = require('https');
const { URL } = require('url');

// 配置
const CONFIG = {
  API_BASE_URL: 'http://localhost:3000',
  FRONTEND_URL: 'http://localhost:80'
};

// 工具函数
function logInfo(message) {
  console.log(`ℹ️  ${message}`);
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message) {
  console.log(`❌ ${message}`);
}

function logWarning(message) {
  console.log(`⚠️  ${message}`);
}

// HTTP请求函数
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 5000
    };
    
    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// 测试API网关连接
async function testApiGateway() {
  try {
    logInfo('测试API网关连接...');
    const response = await makeRequest(`${CONFIG.API_BASE_URL}/api/health`);
    
    if (response.status === 200) {
      logSuccess('API网关连接正常');
      return true;
    } else {
      logError(`API网关响应异常: ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`API网关连接失败: ${error.message}`);
    return false;
  }
}

// 测试前端连接
async function testFrontend() {
  try {
    logInfo('测试前端连接...');
    const response = await makeRequest(CONFIG.FRONTEND_URL);
    
    if (response.status === 200) {
      logSuccess('前端连接正常');
      return true;
    } else {
      logError(`前端响应异常: ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`前端连接失败: ${error.message}`);
    return false;
  }
}

// 测试场景数据API路由（不需要认证的测试）
async function testSceneDataRoute() {
  try {
    logInfo('测试场景数据API路由...');
    // 这个请求会返回401（未认证），但说明路由存在
    const response = await makeRequest(`${CONFIG.API_BASE_URL}/api/projects/test/scenes/test/data`);
    
    if (response.status === 401) {
      logSuccess('场景数据API路由存在（返回401未认证，符合预期）');
      return true;
    } else if (response.status === 404) {
      logError('场景数据API路由不存在（返回404）');
      return false;
    } else {
      logWarning(`场景数据API路由响应: ${response.status}`);
      return true;
    }
  } catch (error) {
    logError(`场景数据API路由测试失败: ${error.message}`);
    return false;
  }
}

// 检查Docker容器状态
async function checkDockerContainers() {
  try {
    logInfo('检查关键服务状态...');
    
    const services = [
      { name: 'API网关', url: `${CONFIG.API_BASE_URL}/api/health` },
      { name: '前端编辑器', url: CONFIG.FRONTEND_URL },
      { name: '服务注册中心', url: 'http://localhost:4010/api/health' },
      { name: '用户服务', url: 'http://localhost:4001/api/health' },
      { name: '项目服务', url: 'http://localhost:4002/api/health' }
    ];
    
    const results = [];
    
    for (const service of services) {
      try {
        const response = await makeRequest(service.url, { timeout: 3000 });
        if (response.status === 200) {
          logSuccess(`${service.name}: 运行正常`);
          results.push({ name: service.name, status: 'healthy' });
        } else {
          logWarning(`${service.name}: 响应异常 (${response.status})`);
          results.push({ name: service.name, status: 'unhealthy' });
        }
      } catch (error) {
        logError(`${service.name}: 连接失败 (${error.message})`);
        results.push({ name: service.name, status: 'down' });
      }
    }
    
    return results;
  } catch (error) {
    logError(`检查服务状态失败: ${error.message}`);
    return [];
  }
}

// 生成诊断报告
function generateDiagnosticReport(serviceResults) {
  const report = {
    timestamp: new Date().toISOString(),
    services: serviceResults,
    summary: {
      healthy: serviceResults.filter(s => s.status === 'healthy').length,
      unhealthy: serviceResults.filter(s => s.status === 'unhealthy').length,
      down: serviceResults.filter(s => s.status === 'down').length
    },
    recommendations: []
  };
  
  // 生成建议
  if (report.summary.down > 0) {
    report.recommendations.push('有服务未启动，请检查Docker容器状态');
  }
  
  if (report.summary.unhealthy > 0) {
    report.recommendations.push('有服务响应异常，请检查服务日志');
  }
  
  if (report.summary.healthy === serviceResults.length) {
    report.recommendations.push('所有服务运行正常，项目卡片点击功能应该可以正常工作');
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 诊断报告');
  console.log('='.repeat(60));
  console.log(`健康服务: ${report.summary.healthy}`);
  console.log(`异常服务: ${report.summary.unhealthy}`);
  console.log(`停止服务: ${report.summary.down}`);
  console.log('\n建议:');
  report.recommendations.forEach(rec => console.log(`  • ${rec}`));
  console.log('='.repeat(60));
  
  return report;
}

// 主测试函数
async function runDiagnostics() {
  console.log('🔍 项目卡片点击功能诊断');
  console.log('='.repeat(60));
  
  try {
    // 1. 测试基础连接
    await testApiGateway();
    await testFrontend();
    
    // 2. 测试关键API路由
    await testSceneDataRoute();
    
    // 3. 检查所有服务状态
    const serviceResults = await checkDockerContainers();
    
    // 4. 生成诊断报告
    const report = generateDiagnosticReport(serviceResults);
    
    logInfo('诊断完成');
    
    return report;
    
  } catch (error) {
    logError(`诊断过程中发生错误: ${error.message}`);
    return null;
  }
}

// 运行诊断
if (require.main === module) {
  runDiagnostics().then(report => {
    if (report && report.summary.healthy >= 3) {
      console.log('\n✅ 系统状态良好，项目卡片点击功能应该可以正常工作');
      process.exit(0);
    } else {
      console.log('\n❌ 系统存在问题，需要修复后再测试项目卡片点击功能');
      process.exit(1);
    }
  }).catch(error => {
    console.error('诊断失败:', error);
    process.exit(1);
  });
}

module.exports = { runDiagnostics, CONFIG };
