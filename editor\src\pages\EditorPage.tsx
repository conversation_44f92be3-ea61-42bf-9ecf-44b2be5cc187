/**
 * 编辑器页面
 */
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Spin, message} from 'antd';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import { fetchProjectById, fetchProjectScenes, setCurrentProject, setCurrentScene } from '../store/project/projectSlice';
import { loadScene } from '../store/editor/editorSlice';
import { EditorLayout } from '../components/layout/EditorLayout';
import { SimpleEditorLayout } from '../components/layout/SimpleEditorLayout';

export const EditorPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { projectId, sceneId } = useParams<{ projectId: string; sceneId: string }>();
  
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const { currentProject, currentScene, isLoading: projectLoading, error: projectError } = useAppSelector((state) => state.project);
  const { isLoading: editorLoading, error: editorError } = useAppSelector((state) => state.editor);
  
  const [isInitialized, setIsInitialized] = useState(false);
  
  // 检查认证状态
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: `/editor/${projectId}/${sceneId}` } });
    }
  }, [isAuthenticated, navigate, projectId, sceneId]);
  
  // 加载项目和场景
  useEffect(() => {
    if (!projectId || !sceneId) {
      console.warn('缺少项目ID或场景ID，返回项目列表');
      navigate('/projects');
      return;
    }

    console.log('开始加载项目和场景:', { projectId, sceneId });

    const loadProjectAndScene = async () => {
      try {
        console.log('步骤1: 加载项目详情...');
        // 加载项目
        const project = await dispatch(fetchProjectById(projectId)).unwrap();
        console.log('项目加载成功:', project);

        // 设置当前项目
        dispatch(setCurrentProject(project));

        console.log('步骤2: 获取项目场景列表...');
        // 获取项目场景列表
        const scenes = await dispatch(fetchProjectScenes(projectId)).unwrap();
        console.log('场景列表获取成功:', scenes);

        // 找到当前场景
        const currentScene = scenes.find((scene: any) => scene.id === sceneId);
        if (currentScene) {
          console.log('设置当前场景:', currentScene);
          dispatch(setCurrentScene(currentScene));
        } else {
          console.warn('未找到指定的场景:', sceneId);
          message.error('指定的场景不存在');
          navigate('/projects');
          return;
        }

        console.log('步骤3: 加载场景数据...');
        // 加载场景数据
        try {
          const sceneData = await dispatch(loadScene({ projectId, sceneId })).unwrap();
          console.log('场景数据加载成功:', sceneData);

          // 如果场景数据为空，创建默认场景数据
          if (!sceneData || Object.keys(sceneData).length === 0) {
            console.log('场景数据为空，使用默认场景数据');
            // 这里可以设置默认的场景数据到Redux状态中
          }
        } catch (sceneError) {
          console.warn('场景数据加载失败，但继续初始化编辑器:', sceneError);
          // 场景数据加载失败不应该阻止编辑器初始化
          // 可以在这里设置一个标志，表示编辑器以空场景模式启动
          message.info('场景数据加载失败，编辑器将以空场景模式启动');
        }

        setIsInitialized(true);
        console.log('编辑器初始化完成');
      } catch (error) {
        console.error('加载项目和场景失败:', error);

        // 提供更详细的错误信息
        let errorMessage = t('editor.loadError');
        if (typeof error === 'string') {
          errorMessage = error;
        } else if (error && typeof error === 'object' && 'message' in error) {
          errorMessage = error.message;
        }

        console.error('错误详情:', errorMessage);

        // 根据错误类型提供不同的处理方式
        if (errorMessage.includes('权限') || errorMessage.includes('认证') || errorMessage.includes('401')) {
          message.error('登录已过期，请重新登录');
          navigate('/login');
        } else if (errorMessage.includes('404') || errorMessage.includes('不存在')) {
          message.error('项目或场景不存在，返回项目列表');
          navigate('/projects');
        } else if (errorMessage.includes('网络') || errorMessage.includes('连接')) {
          message.error('网络连接失败，请检查网络后重试');
          // 不自动跳转，让用户可以重试
        } else {
          message.error(errorMessage);
          navigate('/projects');
        }
      }
    };

    loadProjectAndScene();
  }, [dispatch, navigate, projectId, sceneId, t]);
  
  // 处理错误
  useEffect(() => {
    if (projectError) {
      message.error(projectError);
    }
    
    if (editorError) {
      message.error(editorError);
    }
  }, [projectError, editorError]);
  
  // 处理离开编辑器
  const handleBeforeUnload = (e: BeforeUnloadEvent) => {
    const message = t('editor.unsavedChanges');
    e.returnValue = message;
    return message;
  };
  
  // 添加离开提示
  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);
  
  // 如果正在加载，显示加载状态
  if (projectLoading || editorLoading || !isInitialized) {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip={t('editor.loading')} />
        {/* 只在开发环境显示调试信息 */}
        {process.env.NODE_ENV === 'development' && (
          <div style={{ marginTop: '20px', textAlign: 'center', fontSize: '12px', color: '#666' }}>
            <p>项目ID: {projectId}</p>
            <p>场景ID: {sceneId}</p>
            <p>认证状态: {isAuthenticated ? '已认证' : '未认证'}</p>
            <p>项目加载中: {projectLoading ? '是' : '否'}</p>
            <p>编辑器加载中: {editorLoading ? '是' : '否'}</p>
            <p>已初始化: {isInitialized ? '是' : '否'}</p>
            {projectError && <p style={{ color: 'red' }}>项目错误: {projectError}</p>}
            {editorError && <p style={{ color: 'red' }}>编辑器错误: {editorError}</p>}
          </div>
        )}
      </div>
    );
  }

  // 如果已经初始化但没有项目或场景，显示错误
  if (isInitialized && (!currentProject || !currentScene)) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <div style={{ textAlign: 'center' }}>
          <h2>{t('editor.loadError')}</h2>
          <p>{t('editor.projectOrSceneNotFound')}</p>
          <button onClick={() => navigate('/projects')}>{t('editor.backToProjects')}</button>
        </div>
      </div>
    );
  }
  
  // 使用简化布局进行测试
  const useSimpleLayout = false; // 设置为false使用完整布局

  return useSimpleLayout ? (
    <SimpleEditorLayout projectId={projectId!} sceneId={sceneId!} />
  ) : (
    <EditorLayout projectId={projectId!} sceneId={sceneId!} />
  );
};
