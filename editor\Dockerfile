FROM node:22-alpine AS builder

WORKDIR /app

# 复制engine依赖（已预构建）
COPY engine ./engine

# 复制编辑器的package.json和package-lock.json
COPY editor/package*.json ./

# 临时修改package.json中的engine路径
RUN sed -i 's|"dl-engine-core": "file:../engine"|"dl-engine-core": "file:./engine"|g' package.json

# 设置npm配置
RUN npm config set registry https://registry.npmmirror.com/ && \
    npm config set fund false && \
    npm config set audit false

# 安装依赖
RUN npm install --verbose

# 复制编辑器源代码（排除node_modules）
COPY editor/src ./src
COPY editor/public ./public
COPY editor/scripts ./scripts
COPY editor/index.html ./
COPY editor/vite.config.ts ./
COPY editor/tsconfig.json ./
COPY editor/tsconfig.node.json ./
COPY editor/jest.config.js ./

# 设置环境变量
ENV REACT_APP_API_URL=/api
ENV REACT_APP_COLLABORATION_SERVER_URL=/ws
ENV REACT_APP_MINIO_ENDPOINT=http://localhost:9000
ENV REACT_APP_ENVIRONMENT=production
ENV NODE_ENV=production

# 构建应用
RUN npm run build

# 注入环境变量到构建产物
RUN node scripts/inject-env.js

# 生产环境
FROM nginx:alpine

# 配置Alpine镜像源并安装curl用于健康检查
RUN echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/main" > /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/community" >> /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache curl ca-certificates

# 复制构建产物到Nginx服务目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制Nginx配置
COPY editor/nginx.conf /etc/nginx/conf.d/default.conf

# 创建nginx启动脚本来处理DNS解析问题
RUN echo '#!/bin/sh' > /docker-entrypoint.sh && \
    echo 'echo "Starting nginx with DNS resolver configuration..."' >> /docker-entrypoint.sh && \
    echo 'nginx -t' >> /docker-entrypoint.sh && \
    echo 'if [ $? -eq 0 ]; then' >> /docker-entrypoint.sh && \
    echo '  echo "Nginx configuration is valid"' >> /docker-entrypoint.sh && \
    echo '  exec nginx -g "daemon off;"' >> /docker-entrypoint.sh && \
    echo 'else' >> /docker-entrypoint.sh && \
    echo '  echo "Nginx configuration test failed"' >> /docker-entrypoint.sh && \
    echo '  exit 1' >> /docker-entrypoint.sh && \
    echo 'fi' >> /docker-entrypoint.sh && \
    chmod +x /docker-entrypoint.sh

# 暴露端口
EXPOSE 80

# 启动脚本
ENTRYPOINT ["/docker-entrypoint.sh"]
