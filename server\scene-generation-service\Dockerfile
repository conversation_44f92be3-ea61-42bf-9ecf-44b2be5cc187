# 使用官方Node.js 22镜像作为基础镜像
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制场景生成服务代码
COPY scene-generation-service/package*.json ./

# 安装依赖
RUN npm install

# 复制场景生成服务源代码
COPY scene-generation-service/src ./src
COPY scene-generation-service/tsconfig.json ./
COPY scene-generation-service/nest-cli.json ./

# 构建应用
RUN npm run build

FROM node:22-alpine

WORKDIR /app

# 配置Alpine镜像源并安装curl用于健康检查
RUN echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/main" > /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/community" >> /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache curl ca-certificates

COPY --from=builder /app/package*.json ./
COPY --from=builder /app/dist ./dist

RUN npm install --only=production

# 创建uploads目录
RUN mkdir -p uploads

# 创建logs目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 8005

# 设置环境变量
ENV NODE_ENV=production

# 启动应用
CMD ["node", "dist/main.js"]
