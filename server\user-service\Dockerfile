FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制用户服务代码
COPY user-service/package*.json ./
RUN npm install

# 复制用户服务源代码
COPY user-service/src ./src
COPY user-service/tsconfig.json ./
COPY user-service/nest-cli.json ./
COPY user-service/health-check.js ./

RUN npm run build

FROM node:22-alpine

# 配置Alpine镜像源并安装curl用于健康检查
RUN echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/main" > /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/community" >> /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache curl ca-certificates

WORKDIR /app

COPY --from=builder /app/package*.json ./
COPY --from=builder /app/dist ./dist

# 复制健康检查脚本
COPY --from=builder /app/health-check.js ./health-check.js

RUN npm install --only=production

EXPOSE 3001 4001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:4001/api/health || exit 1

CMD ["node", "dist/main.js"]
