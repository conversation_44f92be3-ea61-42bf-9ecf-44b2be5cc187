#!/usr/bin/env pwsh
# Docker构建修复验证脚本
# 用于测试Alpine Linux包管理器修复是否有效

param(
    [string]$Service = "project-service",
    [switch]$AllServices = $false,
    [switch]$Verbose = $false
)

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🚀 $message"
    Write-Host "=" * 60
}

# 测试单个服务构建
function Test-ServiceBuild {
    param(
        [string]$ServiceName,
        [string]$Context,
        [string]$DockerfilePath
    )
    
    Write-Info "测试构建服务: $ServiceName"
    Write-Info "构建上下文: $Context"
    Write-Info "Dockerfile路径: $DockerfilePath"
    
    try {
        $buildCommand = "docker build --no-cache -f `"$DockerfilePath`" -t `"test-$ServiceName`" `"$Context`""
        
        if ($Verbose) {
            Write-Info "执行命令: $buildCommand"
        }
        
        $result = Invoke-Expression $buildCommand 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "$ServiceName 构建成功"
            
            # 清理测试镜像
            docker rmi "test-$ServiceName" -f | Out-Null
            
            return $true
        } else {
            Write-Error "$ServiceName 构建失败"
            if ($Verbose) {
                Write-Host $result
            }
            return $false
        }
    } catch {
        Write-Error "$ServiceName 构建异常: $($_.Exception.Message)"
        return $false
    }
}

# 服务配置
$services = @{
    "project-service" = @{
        Context = "./server"
        Dockerfile = "project-service/Dockerfile"
    }
    "user-service" = @{
        Context = "./server"
        Dockerfile = "user-service/Dockerfile"
    }
    "service-registry" = @{
        Context = "./server"
        Dockerfile = "service-registry/Dockerfile"
    }
    "api-gateway" = @{
        Context = "./server/api-gateway"
        Dockerfile = "Dockerfile"
    }
    "render-service" = @{
        Context = "./server"
        Dockerfile = "render-service/Dockerfile"
    }
    "asset-service" = @{
        Context = "./server/asset-service"
        Dockerfile = "Dockerfile"
    }
    "ai-model-service" = @{
        Context = "./server"
        Dockerfile = "ai-model-service/Dockerfile"
    }
    "knowledge-service" = @{
        Context = "./server/knowledge-service"
        Dockerfile = "Dockerfile"
    }
    "rag-engine" = @{
        Context = "./server/rag-engine"
        Dockerfile = "Dockerfile"
    }
    "asset-library-service" = @{
        Context = "./server/asset-library-service"
        Dockerfile = "Dockerfile"
    }
    "binding-service" = @{
        Context = "./server/binding-service"
        Dockerfile = "Dockerfile"
    }
    "scene-generation-service" = @{
        Context = "./server"
        Dockerfile = "scene-generation-service/Dockerfile"
    }
    "scene-template-service" = @{
        Context = "./server"
        Dockerfile = "scene-template-service/Dockerfile"
    }
    "collaboration-service" = @{
        Context = "./server"
        Dockerfile = "collaboration-service/Dockerfile"
    }
    "monitoring-service" = @{
        Context = "./server"
        Dockerfile = "monitoring-service/Dockerfile"
    }
    "editor" = @{
        Context = "."
        Dockerfile = "editor/Dockerfile"
    }
}

Write-Header "Docker构建修复验证"

# 检查Docker是否可用
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Error "Docker未安装或不在PATH中"
    exit 1
}

Write-Info "Docker版本信息:"
docker --version

$successCount = 0
$totalCount = 0

if ($AllServices) {
    Write-Header "测试所有服务构建"
    
    foreach ($serviceName in $services.Keys) {
        $serviceConfig = $services[$serviceName]
        $totalCount++
        
        $success = Test-ServiceBuild -ServiceName $serviceName -Context $serviceConfig.Context -DockerfilePath $serviceConfig.Dockerfile
        if ($success) {
            $successCount++
        }
        
        Write-Host ""
    }
} else {
    Write-Header "测试单个服务构建: $Service"
    
    if ($services.ContainsKey($Service)) {
        $serviceConfig = $services[$Service]
        $totalCount = 1
        
        $success = Test-ServiceBuild -ServiceName $Service -Context $serviceConfig.Context -DockerfilePath $serviceConfig.Dockerfile
        if ($success) {
            $successCount = 1
        }
    } else {
        Write-Error "未知服务: $Service"
        Write-Info "可用服务: $($services.Keys -join ', ')"
        exit 1
    }
}

Write-Header "构建测试结果"
Write-Info "总计: $totalCount 个服务"
Write-Success "成功: $successCount 个服务"
Write-Error "失败: $($totalCount - $successCount) 个服务"

if ($successCount -eq $totalCount) {
    Write-Success "所有服务构建测试通过！Alpine Linux包管理器修复有效。"
    exit 0
} else {
    Write-Error "部分服务构建失败，需要进一步检查。"
    exit 1
}
