# 使用官方 Node.js 运行时作为基础镜像
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制AI模型服务代码
COPY ai-model-service/package*.json ./

# 安装依赖
RUN npm install

# 复制AI模型服务源代码
COPY ai-model-service/src ./src
COPY ai-model-service/tsconfig.json ./
COPY ai-model-service/nest-cli.json ./

# 构建应用
RUN npm run build

FROM node:22-alpine

WORKDIR /app

# 配置Alpine镜像源并安装curl用于健康检查
RUN echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/main" > /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/v3.22/community" >> /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache curl ca-certificates

COPY --from=builder /app/package*.json ./
COPY --from=builder /app/dist ./dist

RUN npm install --only=production

# 暴露端口
EXPOSE 3008 3018

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 切换到非 root 用户
USER nestjs

# 启动应用
CMD ["node", "dist/main.js"]
