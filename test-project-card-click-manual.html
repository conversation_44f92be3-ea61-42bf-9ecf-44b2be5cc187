<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目卡片点击功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .test-button.secondary {
            background: #2196F3;
        }
        
        .test-button.secondary:hover {
            background: #1976D2;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .step-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .step-list ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .step-list li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .info-card h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.green {
            background-color: #28a745;
        }
        
        .status-indicator.red {
            background-color: #dc3545;
        }
        
        .status-indicator.yellow {
            background-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 项目卡片点击功能测试</h1>
        <p>验证项目卡片点击后能否正常进入编辑器界面</p>
    </div>

    <div class="test-section">
        <h2>📋 测试步骤</h2>
        <div class="step-list">
            <ol>
                <li><strong>检查系统状态</strong> - 验证所有服务是否正常运行</li>
                <li><strong>测试API连接</strong> - 确认前端能够连接到后端API</li>
                <li><strong>创建测试数据</strong> - 创建测试用户、项目和场景</li>
                <li><strong>测试项目卡片点击</strong> - 模拟用户点击项目卡片</li>
                <li><strong>验证编辑器加载</strong> - 确认编辑器页面正常加载</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 系统状态检查</h2>
        <button class="test-button" onclick="checkSystemStatus()">检查系统状态</button>
        <button class="test-button secondary" onclick="checkApiRoutes()">检查API路由</button>
        <div id="systemStatus" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🧪 API测试</h2>
        <button class="test-button" onclick="testApiConnection()">测试API连接</button>
        <button class="test-button secondary" onclick="testSceneDataApi()">测试场景数据API</button>
        <div id="apiTestResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🎮 项目卡片点击模拟</h2>
        <p>这个测试将模拟用户点击项目卡片的完整流程：</p>
        <button class="test-button" onclick="simulateProjectCardClick()">模拟项目卡片点击</button>
        <button class="test-button secondary" onclick="openEditorDirectly()">直接打开编辑器</button>
        <div id="projectCardResult" class="result" style="display: none;"></div>
    </div>

    <div class="info-grid">
        <div class="info-card">
            <h3>🌐 访问地址</h3>
            <p><strong>前端编辑器:</strong> <a href="http://localhost" target="_blank">http://localhost</a></p>
            <p><strong>API网关:</strong> <a href="http://localhost:3000/api/health" target="_blank">http://localhost:3000</a></p>
            <p><strong>服务注册中心:</strong> <a href="http://localhost:4010" target="_blank">http://localhost:4010</a></p>
        </div>
        <div class="info-card">
            <h3>🔍 诊断信息</h3>
            <p><strong>测试时间:</strong> <span id="testTime">-</span></p>
            <p><strong>浏览器:</strong> <span id="browserInfo">-</span></p>
            <p><strong>系统状态:</strong> <span id="systemStatusIndicator">-</span></p>
        </div>
    </div>

    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('testTime').textContent = new Date().toLocaleString();
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
        });

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        // 检查系统状态
        async function checkSystemStatus() {
            showResult('systemStatus', '正在检查系统状态...', 'info');
            
            try {
                const services = [
                    { name: 'API网关', url: 'http://localhost:3000/api/health' },
                    { name: '前端编辑器', url: 'http://localhost' },
                    { name: '服务注册中心', url: 'http://localhost:4010/api/health' },
                    { name: '用户服务', url: 'http://localhost:4001/api/health' },
                    { name: '项目服务', url: 'http://localhost:4002/api/health' }
                ];
                
                let results = [];
                let healthyCount = 0;
                
                for (const service of services) {
                    try {
                        const response = await fetch(service.url, { 
                            method: 'GET',
                            mode: 'cors',
                            timeout: 5000 
                        });
                        
                        if (response.ok) {
                            results.push(`<span class="status-indicator green"></span>${service.name}: 正常`);
                            healthyCount++;
                        } else {
                            results.push(`<span class="status-indicator yellow"></span>${service.name}: 响应异常 (${response.status})`);
                        }
                    } catch (error) {
                        results.push(`<span class="status-indicator red"></span>${service.name}: 连接失败`);
                    }
                }
                
                const statusHtml = `
                    <h4>服务状态检查结果:</h4>
                    ${results.join('<br>')}
                    <br><br>
                    <strong>总结:</strong> ${healthyCount}/${services.length} 个服务正常运行
                `;
                
                const statusType = healthyCount >= 3 ? 'success' : 'error';
                showResult('systemStatus', statusHtml, statusType);
                
                // 更新状态指示器
                const indicator = healthyCount >= 3 ? 
                    '<span class="status-indicator green"></span>正常' : 
                    '<span class="status-indicator red"></span>异常';
                document.getElementById('systemStatusIndicator').innerHTML = indicator;
                
            } catch (error) {
                showResult('systemStatus', `系统状态检查失败: ${error.message}`, 'error');
            }
        }

        // 检查API路由
        async function checkApiRoutes() {
            showResult('systemStatus', '正在检查API路由...', 'info');
            
            try {
                // 测试场景数据API路由（应该返回401）
                const response = await fetch('http://localhost:3000/api/projects/test/scenes/test/data');
                
                if (response.status === 401) {
                    showResult('systemStatus', 
                        '✅ 场景数据API路由存在且正常工作<br>' +
                        '路径: /api/projects/:projectId/scenes/:sceneId/data<br>' +
                        '状态: 返回401（未认证），符合预期', 'success');
                } else if (response.status === 404) {
                    showResult('systemStatus', 
                        '❌ 场景数据API路由不存在<br>' +
                        '路径: /api/projects/:projectId/scenes/:sceneId/data<br>' +
                        '状态: 返回404（未找到）', 'error');
                } else {
                    showResult('systemStatus', 
                        `⚠️ 场景数据API路由响应异常<br>` +
                        `路径: /api/projects/:projectId/scenes/:sceneId/data<br>` +
                        `状态: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('systemStatus', `API路由检查失败: ${error.message}`, 'error');
            }
        }

        // 测试API连接
        async function testApiConnection() {
            showResult('apiTestResult', '正在测试API连接...', 'info');
            
            try {
                const response = await fetch('http://localhost:3000/api/health');
                
                if (response.ok) {
                    const data = await response.text();
                    showResult('apiTestResult', 
                        `✅ API连接成功<br>` +
                        `状态码: ${response.status}<br>` +
                        `响应: ${data}`, 'success');
                } else {
                    showResult('apiTestResult', 
                        `❌ API连接失败<br>状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('apiTestResult', `API连接测试失败: ${error.message}`, 'error');
            }
        }

        // 测试场景数据API
        async function testSceneDataApi() {
            showResult('apiTestResult', '正在测试场景数据API...', 'info');
            
            try {
                const response = await fetch('http://localhost:3000/api/projects/test-project/scenes/test-scene/data');
                
                if (response.status === 401) {
                    showResult('apiTestResult', 
                        `✅ 场景数据API正常<br>` +
                        `路径: /api/projects/test-project/scenes/test-scene/data<br>` +
                        `状态: 401（需要认证），符合预期`, 'success');
                } else {
                    showResult('apiTestResult', 
                        `⚠️ 场景数据API响应: ${response.status}<br>` +
                        `这可能表示API路由配置有问题`, 'error');
                }
            } catch (error) {
                showResult('apiTestResult', `场景数据API测试失败: ${error.message}`, 'error');
            }
        }

        // 模拟项目卡片点击
        function simulateProjectCardClick() {
            showResult('projectCardResult', '正在模拟项目卡片点击...', 'info');
            
            // 模拟项目卡片点击的完整流程
            const testProjectId = 'test-project-' + Date.now();
            const testSceneId = 'test-scene-' + Date.now();
            
            const simulationSteps = [
                '1. 用户点击项目卡片',
                '2. 前端调用 fetchProjectScenes API',
                '3. 获取项目场景列表',
                '4. 跳转到编辑器路由: /editor/' + testProjectId + '/' + testSceneId,
                '5. EditorPage 组件加载',
                '6. 调用 loadScene API 获取场景数据',
                '7. 编辑器初始化完成'
            ];
            
            let currentStep = 0;
            const interval = setInterval(() => {
                if (currentStep < simulationSteps.length) {
                    const progress = simulationSteps.slice(0, currentStep + 1).join('<br>');
                    showResult('projectCardResult', 
                        `<h4>模拟进度:</h4>${progress}`, 'info');
                    currentStep++;
                } else {
                    clearInterval(interval);
                    showResult('projectCardResult', 
                        `<h4>模拟完成!</h4>` +
                        `${simulationSteps.join('<br>')}<br><br>` +
                        `<strong>结论:</strong> 如果所有API路由正常，项目卡片点击功能应该可以正常工作。<br>` +
                        `<strong>测试路由:</strong> /editor/${testProjectId}/${testSceneId}`, 'success');
                }
            }, 800);
        }

        // 直接打开编辑器
        function openEditorDirectly() {
            const testUrl = `http://localhost/editor/test-project-${Date.now()}/test-scene-${Date.now()}`;
            showResult('projectCardResult', 
                `正在打开编辑器页面...<br>` +
                `URL: ${testUrl}<br>` +
                `请检查新打开的标签页是否正常加载编辑器界面。`, 'info');
            
            window.open(testUrl, '_blank');
        }

        // 自动检查系统状态
        setTimeout(checkSystemStatus, 1000);
    </script>
</body>
</html>
